<template>
  <div class="office-viewer">
    <div class="office-toolbar">
      <div class="toolbar-left">
        <el-tag :type="getFileTypeTag(fileType)" size="small">{{ getFileTypeLabel(fileType) }}</el-tag>
        <span class="file-name">{{ fileName }}</span>
      </div>
      <div class="toolbar-right">
        <el-button-group size="small">
          <el-button :type="viewMode === 'content' ? 'primary' : ''" @click="viewMode = 'content'">
            内容
          </el-button>
          <el-button :type="viewMode === 'structure' ? 'primary' : ''" @click="viewMode = 'structure'">
            结构
          </el-button>
          <el-button :type="viewMode === 'metadata' ? 'primary' : ''" @click="viewMode = 'metadata'">
            属性
          </el-button>
        </el-button-group>
      </div>
    </div>

    <div class="office-content" v-loading="loading">
      <!-- 内容视图 -->
      <div v-if="viewMode === 'content'" class="content-view">
        <!-- Word文档内容 -->
        <div v-if="fileType === 'word'" class="word-content">
          <div class="document-title">{{ documentData.title || fileName }}</div>
          <div class="document-body" v-html="documentData.content"></div>
        </div>

        <!-- Excel表格内容 -->
        <div v-else-if="fileType === 'excel'" class="excel-content">
          <el-tabs v-model="activeSheet" @tab-click="handleSheetChange">
            <el-tab-pane v-for="(sheet, index) in documentData.sheets" :key="index" :label="sheet.name"
              :name="index.toString()">
              <div class="excel-table">
                <table>
                  <thead>
                    <tr>
                      <th v-for="(header, colIndex) in sheet.headers" :key="colIndex">
                        {{ header }}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(row, rowIndex) in sheet.data" :key="rowIndex">
                      <td v-for="(cell, colIndex) in row" :key="colIndex">
                        {{ cell }}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>

        <!-- PowerPoint幻灯片内容 -->
        <div v-else-if="fileType === 'powerpoint'" class="powerpoint-content">
          <div class="slide-navigation">
            <el-button-group size="small">
              <el-button icon="el-icon-arrow-left" @click="prevSlide" :disabled="currentSlide <= 0">
                上一张
              </el-button>
              <span class="slide-info">{{ currentSlide + 1 }} / {{ documentData.slides.length }}</span>
              <el-button icon="el-icon-arrow-right" @click="nextSlide"
                :disabled="currentSlide >= documentData.slides.length - 1">
                下一张
              </el-button>
            </el-button-group>
          </div>
          <div class="slide-content">
            <div class="slide" v-if="documentData.slides[currentSlide]">
              <h2 class="slide-title">{{ documentData.slides[currentSlide].title }}</h2>
              <div class="slide-body" v-html="documentData.slides[currentSlide].content"></div>
            </div>
          </div>
          <div class="slide-thumbnails">
            <div v-for="(slide, index) in documentData.slides" :key="index" class="slide-thumbnail"
              :class="{ active: index === currentSlide }" @click="currentSlide = index">
              <div class="thumbnail-title">{{ slide.title }}</div>
              <div class="thumbnail-number">{{ index + 1 }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 结构视图 -->
      <div v-else-if="viewMode === 'structure'" class="structure-view">
        <el-tree :data="structureData" :props="{ children: 'children', label: 'label' }" default-expand-all />
      </div>

      <!-- 属性视图 -->
      <div v-else-if="viewMode === 'metadata'" class="metadata-view">
        <el-descriptions title="文档属性" :column="2" border>
          <el-descriptions-item label="文件名">{{ fileName }}</el-descriptions-item>
          <el-descriptions-item label="文件类型">{{ getFileTypeLabel(fileType) }}</el-descriptions-item>
          <el-descriptions-item label="文件大小">{{ formatFileSize(fileSize) }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ (documentData.metadata && documentData.metadata.created) || '未知'
            }}</el-descriptions-item>
          <el-descriptions-item label="修改时间">{{ (documentData.metadata && documentData.metadata.modified) || '未知'
            }}</el-descriptions-item>
          <el-descriptions-item label="作者">{{ (documentData.metadata && documentData.metadata.author) || '未知'
            }}</el-descriptions-item>
          <el-descriptions-item label="页数/张数" v-if="fileType === 'word'">{{ (documentData.metadata &&
            documentData.metadata.pages) || '未知' }}</el-descriptions-item>
          <el-descriptions-item label="工作表数" v-if="fileType === 'excel'">{{ (documentData.sheets &&
            documentData.sheets.length) || 0 }}</el-descriptions-item>
          <el-descriptions-item label="幻灯片数" v-if="fileType === 'powerpoint'">{{ (documentData.slides &&
            documentData.slides.length) || 0 }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 错误状态 -->
      <div v-if="error" class="office-error">
        <i class="el-icon-warning"></i>
        <h3>文档解析失败</h3>
        <p>{{ error }}</p>
        <el-button @click="reload">重新加载</el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'OfficeViewer',
  props: {
    src: {
      type: String,
      required: true
    },
    fileName: {
      type: String,
      default: '未知文档'
    },
    fileSize: {
      type: Number,
      default: 0
    }
  },
  data () {
    return {
      loading: false,
      error: null,
      viewMode: 'content',
      documentData: {},
      structureData: [],
      activeSheet: '0',
      currentSlide: 0
    }
  },
  computed: {
    fileType () {
      const ext = this.fileName.split('.').pop().toLowerCase()
      if (['doc', 'docx'].includes(ext)) return 'word'
      if (['xls', 'xlsx'].includes(ext)) return 'excel'
      if (['ppt', 'pptx'].includes(ext)) return 'powerpoint'
      return 'unknown'
    }
  },
  watch: {
    src: {
      immediate: true,
      handler (newSrc) {
        if (newSrc) {
          this.loadDocument()
        }
      }
    }
  },
  methods: {
    async loadDocument () {
      this.loading = true
      this.error = null

      try {
        // 尝试从静态JSON文件加载真实数据
        const jsonUrl = this.src + '.json'
        try {
          const response = await fetch(jsonUrl)
          if (response.ok) {
            const jsonData = await response.json()
            if (jsonData.type === this.fileType) {
              this.documentData = jsonData
              this.structureData = this.generateStructureFromData(jsonData)
              return
            }
          }
        } catch (jsonError) {
          console.log('JSON文件加载失败，使用模拟数据:', jsonError)
        }

        // 模拟文档解析，实际项目中应该调用后端API进行文档解析
        await new Promise(resolve => setTimeout(resolve, 1000))

        this.documentData = this.generateMockDocumentData()
        this.structureData = this.generateStructureData()
      } catch (error) {
        console.error('文档加载失败:', error)
        this.error = error.message || '文档解析失败'
      } finally {
        this.loading = false
      }
    },

    generateMockDocumentData () {
      switch (this.fileType) {
        case 'word':
          return {
            title: '项目需求分析报告',
            content: `
              <h1>项目需求分析报告</h1>
              <h2>1. 项目概述</h2>
              <p>本项目旨在开发一个现代化的工作空间管理系统，支持文件管理、协同编辑、实时预览等功能。</p>

              <h2>2. 功能需求</h2>
              <h3>2.1 文件管理</h3>
              <ul>
                <li>文件上传、下载、删除</li>
                <li>文件夹管理</li>
                <li>文件搜索和筛选</li>
              </ul>

              <h3>2.2 协同编辑</h3>
              <ul>
                <li>多用户实时编辑</li>
                <li>版本控制</li>
                <li>冲突解决</li>
              </ul>

              <h2>3. 技术要求</h2>
              <p>系统采用前后端分离架构，前端使用Vue.js框架，后端使用Node.js。</p>
            `,
            metadata: {
              created: '2024-01-15 10:30:00',
              modified: '2024-01-20 16:45:00',
              author: '项目经理',
              pages: 5
            }
          }

        case 'excel':
          return {
            sheets: [
              {
                name: '用户数据',
                headers: ['ID', '姓名', '邮箱', '部门', '角色'],
                data: [
                  ['1', '张三', '<EMAIL>', '研发部', '开发工程师'],
                  ['2', '李四', '<EMAIL>', '产品部', '产品经理'],
                  ['3', '王五', '<EMAIL>', '设计部', 'UI设计师'],
                  ['4', '赵六', '<EMAIL>', '测试部', '测试工程师']
                ]
              },
              {
                name: '项目统计',
                headers: ['项目名称', '开始时间', '结束时间', '状态', '进度'],
                data: [
                  ['工作空间系统', '2024-01-01', '2024-03-31', '进行中', '75%'],
                  ['移动端应用', '2024-02-01', '2024-05-31', '计划中', '0%'],
                  ['数据分析平台', '2023-10-01', '2024-01-31', '已完成', '100%']
                ]
              }
            ],
            metadata: {
              created: '2024-01-10 09:00:00',
              modified: '2024-01-25 14:20:00',
              author: '数据分析师'
            }
          }

        case 'powerpoint':
          return {
            slides: [
              {
                title: '工作空间系统介绍',
                content: '<p>欢迎使用现代化的工作空间管理系统</p><ul><li>高效的文件管理</li><li>实时协同编辑</li><li>多格式文件预览</li></ul>'
              },
              {
                title: '核心功能',
                content: '<h3>文件管理</h3><p>支持多种文件格式的上传、预览和管理</p><h3>协同编辑</h3><p>多用户实时协作，提高工作效率</p>'
              },
              {
                title: '技术架构',
                content: '<p>前端：Vue.js + Element UI</p><p>后端：Node.js + Express</p><p>数据库：MongoDB</p><p>实时通信：WebSocket</p>'
              },
              {
                title: '未来规划',
                content: '<ul><li>移动端支持</li><li>AI智能助手</li><li>更多文件格式支持</li><li>高级权限管理</li></ul>'
              }
            ],
            metadata: {
              created: '2024-01-18 11:15:00',
              modified: '2024-01-22 09:30:00',
              author: '产品经理'
            }
          }

        default:
          return {}
      }
    },

    generateStructureFromData (jsonData) {
      switch (jsonData.type) {
        case 'word':
          return jsonData.structure || [
            {
              label: '文档结构',
              children: [
                { label: `标题：${jsonData.metadata?.title || '未知文档'}` }
              ]
            }
          ]

        case 'excel':
          return [
            {
              label: '工作簿结构',
              children: jsonData.sheets?.map(sheet => ({
                label: `工作表：${sheet.name}`,
                children: [
                  { label: `行数：${sheet.data?.length || 0}` },
                  { label: `列数：${sheet.headers?.length || 0}` }
                ]
              })) || []
            }
          ]

        case 'powerpoint':
          return [
            {
              label: '演示文稿结构',
              children: jsonData.slides?.map((slide, index) => ({
                label: `幻灯片 ${index + 1}：${slide.title}`
              })) || []
            }
          ]

        default:
          return []
      }
    },

    generateStructureData () {
      switch (this.fileType) {
        case 'word':
          return [
            {
              label: '文档结构',
              children: [
                { label: '标题：项目需求分析报告' },
                {
                  label: '1. 项目概述',
                  children: [
                    { label: '项目背景' },
                    { label: '项目目标' }
                  ]
                },
                {
                  label: '2. 功能需求',
                  children: [
                    { label: '2.1 文件管理' },
                    { label: '2.2 协同编辑' }
                  ]
                },
                { label: '3. 技术要求' }
              ]
            }
          ]

        case 'excel':
          return [
            {
              label: '工作簿结构',
              children: this.documentData.sheets?.map(sheet => ({
                label: `工作表：${sheet.name}`,
                children: [
                  { label: `行数：${sheet.data.length}` },
                  { label: `列数：${sheet.headers.length}` }
                ]
              })) || []
            }
          ]

        case 'powerpoint':
          return [
            {
              label: '演示文稿结构',
              children: this.documentData.slides?.map((slide, index) => ({
                label: `幻灯片 ${index + 1}：${slide.title}`
              })) || []
            }
          ]

        default:
          return []
      }
    },

    handleSheetChange (tab) {
      this.activeSheet = tab.name
    },

    prevSlide () {
      if (this.currentSlide > 0) {
        this.currentSlide--
      }
    },

    nextSlide () {
      if (this.currentSlide < this.documentData.slides.length - 1) {
        this.currentSlide++
      }
    },

    getFileTypeTag (type) {
      const tagMap = {
        word: 'primary',
        excel: 'success',
        powerpoint: 'warning'
      }
      return tagMap[type] || 'info'
    },

    getFileTypeLabel (type) {
      const labelMap = {
        word: 'Word文档',
        excel: 'Excel表格',
        powerpoint: 'PowerPoint演示文稿'
      }
      return labelMap[type] || '未知格式'
    },

    formatFileSize (size) {
      if (!size) return '-'
      const units = ['B', 'KB', 'MB', 'GB']
      let index = 0
      while (size >= 1024 && index < units.length - 1) {
        size /= 1024
        index++
      }
      return size.toFixed(2) + ' ' + units[index]
    },

    reload () {
      this.loadDocument()
    }
  }
}
</script>

<style lang="scss" scoped>
.office-viewer {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.office-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .toolbar-left {
    display: flex;
    align-items: center;
    gap: 12px;

    .file-name {
      font-weight: 500;
      color: #303133;
    }
  }
}

.office-content {
  flex: 1;
  overflow: auto;
  background: white;
}

// Word文档样式
.word-content {
  max-width: 800px;
  margin: 0 auto;
  padding: 40px;
  line-height: 1.6;

  .document-title {
    font-size: 24px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 30px;
    color: #303133;
  }

  .document-body {
    ::v-deep {

      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        margin-top: 24px;
        margin-bottom: 16px;
        font-weight: 600;
        line-height: 1.25;
        color: #303133;
      }

      h1 {
        font-size: 2em;
      }

      h2 {
        font-size: 1.5em;
      }

      h3 {
        font-size: 1.25em;
      }

      p {
        margin-bottom: 16px;
        color: #606266;
      }

      ul,
      ol {
        margin-bottom: 16px;
        padding-left: 24px;
        color: #606266;
      }

      li {
        margin-bottom: 4px;
      }
    }
  }
}

// Excel表格样式
.excel-content {
  padding: 20px;

  .excel-table {
    overflow: auto;

    table {
      width: 100%;
      border-collapse: collapse;
      font-size: 14px;

      th,
      td {
        border: 1px solid #e4e7ed;
        padding: 8px 12px;
        text-align: left;
      }

      th {
        background: #f5f7fa;
        font-weight: 600;
        color: #303133;
      }

      td {
        color: #606266;
      }

      tr:nth-child(even) {
        background: #fafbfc;
      }
    }
  }
}

// PowerPoint样式
.powerpoint-content {
  padding: 20px;

  .slide-navigation {
    text-align: center;
    margin-bottom: 20px;

    .slide-info {
      margin: 0 12px;
      font-size: 14px;
      color: #606266;
    }
  }

  .slide-content {
    max-width: 800px;
    margin: 0 auto 30px auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    min-height: 400px;

    .slide {
      padding: 40px;

      .slide-title {
        font-size: 28px;
        font-weight: bold;
        text-align: center;
        margin-bottom: 30px;
        color: #303133;
      }

      .slide-body {
        ::v-deep {
          h3 {
            font-size: 20px;
            margin: 20px 0 10px 0;
            color: #409eff;
          }

          p {
            margin-bottom: 16px;
            color: #606266;
            line-height: 1.6;
          }

          ul {
            margin-bottom: 16px;
            padding-left: 24px;
          }

          li {
            margin-bottom: 8px;
            color: #606266;
          }
        }
      }
    }
  }

  .slide-thumbnails {
    display: flex;
    justify-content: center;
    gap: 12px;
    flex-wrap: wrap;

    .slide-thumbnail {
      width: 120px;
      height: 80px;
      background: white;
      border: 2px solid #e4e7ed;
      border-radius: 4px;
      cursor: pointer;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      transition: all 0.3s ease;

      &:hover {
        border-color: #409eff;
      }

      &.active {
        border-color: #409eff;
        background: #f0f9ff;
      }

      .thumbnail-title {
        font-size: 10px;
        color: #606266;
        text-align: center;
        margin-bottom: 4px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 100%;
        padding: 0 4px;
      }

      .thumbnail-number {
        font-size: 12px;
        font-weight: bold;
        color: #409eff;
      }
    }
  }
}

// 结构视图样式
.structure-view {
  padding: 20px;
}

// 属性视图样式
.metadata-view {
  padding: 20px;
}

// 错误状态样式
.office-error {
  text-align: center;
  color: #909399;
  padding: 60px 20px;

  i {
    font-size: 64px;
    color: #dcdfe6;
    margin-bottom: 16px;
  }

  h3 {
    margin: 0 0 8px 0;
    color: #606266;
  }

  p {
    margin: 0 0 16px 0;
    font-size: 14px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .office-toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .word-content {
    padding: 20px;
  }

  .powerpoint-content {
    .slide-content .slide {
      padding: 20px;

      .slide-title {
        font-size: 20px;
      }
    }

    .slide-thumbnails {
      .slide-thumbnail {
        width: 80px;
        height: 60px;
      }
    }
  }
}
</style>
