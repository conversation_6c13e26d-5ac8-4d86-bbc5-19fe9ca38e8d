{"app": {"name": "工作空间管理系统", "version": "1.0.0", "description": "现代化的协同工作空间平台", "author": "开发团队", "license": "MIT"}, "server": {"host": "0.0.0.0", "port": 3000, "env": "development", "cors": {"enabled": true, "origins": ["http://localhost:8080", "https://workspace.example.com"]}, "rateLimit": {"windowMs": 900000, "max": 100}}, "database": {"type": "mongodb", "host": "localhost", "port": 27017, "name": "workspace_db", "options": {"useNewUrlParser": true, "useUnifiedTopology": true, "maxPoolSize": 10, "serverSelectionTimeoutMS": 5000, "socketTimeoutMS": 45000}}, "redis": {"host": "localhost", "port": 6379, "password": "", "db": 0, "keyPrefix": "workspace:", "ttl": 3600}, "storage": {"provider": "local", "local": {"uploadPath": "./uploads", "maxFileSize": "50MB", "allowedTypes": ["image/jpeg", "image/png", "image/gif", "application/pdf", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "application/vnd.ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "text/plain", "text/markdown", "application/json"]}, "oss": {"region": "oss-cn-hangzhou", "bucket": "workspace-files", "accessKeyId": "${OSS_ACCESS_KEY_ID}", "accessKeySecret": "${OSS_ACCESS_KEY_SECRET}", "endpoint": "https://oss-cn-hangzhou.aliyuncs.com"}}, "auth": {"jwt": {"secret": "${JWT_SECRET}", "expiresIn": "7d", "issuer": "workspace-system", "audience": "workspace-users"}, "session": {"secret": "${SESSION_SECRET}", "resave": false, "saveUninitialized": false, "cookie": {"secure": false, "httpOnly": true, "maxAge": 604800000}}}, "email": {"provider": "smtp", "smtp": {"host": "smtp.example.com", "port": 587, "secure": false, "auth": {"user": "${EMAIL_USER}", "pass": "${EMAIL_PASS}"}}, "from": "<EMAIL>", "templates": {"welcome": "./templates/welcome.html", "resetPassword": "./templates/reset-password.html", "invitation": "./templates/invitation.html"}}, "websocket": {"enabled": true, "port": 3001, "cors": {"origin": "*", "methods": ["GET", "POST"]}, "pingTimeout": 60000, "pingInterval": 25000}, "logging": {"level": "info", "format": "combined", "file": {"enabled": true, "filename": "./logs/app.log", "maxsize": "10MB", "maxFiles": 5}, "console": {"enabled": true, "colorize": true}}, "security": {"helmet": {"enabled": true, "contentSecurityPolicy": {"directives": {"defaultSrc": ["'self'"], "styleSrc": ["'self'", "'unsafe-inline'"], "scriptSrc": ["'self'"], "imgSrc": ["'self'", "data:", "https:"]}}}, "encryption": {"algorithm": "aes-256-gcm", "keyLength": 32, "ivLength": 16}}, "features": {"collaboration": {"enabled": true, "maxConcurrentUsers": 10, "autoSaveInterval": 30000}, "preview": {"enabled": true, "supportedFormats": ["pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "jpg", "jpeg", "png", "gif", "svg", "webp", "txt", "md", "json", "xml", "yaml", "yml", "js", "ts", "vue", "jsx", "tsx", "py", "java", "cpp", "c", "css", "html"], "maxPreviewSize": "100MB"}, "search": {"enabled": true, "engine": "elasticsearch", "indexing": {"enabled": true, "batchSize": 100, "interval": 300000}}, "backup": {"enabled": true, "schedule": "0 2 * * *", "retention": 30, "destination": "./backups"}}, "monitoring": {"metrics": {"enabled": true, "endpoint": "/metrics", "interval": 10000}, "health": {"enabled": true, "endpoint": "/health", "checks": ["database", "redis", "storage"]}}, "development": {"hotReload": true, "debugMode": true, "mockData": true, "apiDelay": 500}}