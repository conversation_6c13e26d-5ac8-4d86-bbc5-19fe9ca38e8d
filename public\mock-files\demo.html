<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工作空间系统演示页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            font-size: 24px;
            color: white;
        }

        .feature-card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.5rem;
        }

        .feature-card p {
            color: #666;
            line-height: 1.6;
        }

        .demo-section {
            background: white;
            border-radius: 15px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .demo-section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 2rem;
            text-align: center;
        }

        .file-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .file-item {
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .file-item:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }

        .file-icon {
            font-size: 48px;
            margin-bottom: 10px;
            color: #667eea;
        }

        .file-name {
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
        }

        .file-size {
            color: #666;
            font-size: 0.9rem;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .stat-card {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            opacity: 0.9;
        }

        .footer {
            text-align: center;
            color: white;
            margin-top: 40px;
            opacity: 0.8;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .container {
                padding: 10px;
            }
            
            .features {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 工作空间管理系统</h1>
            <p>现代化的协同工作平台，让团队协作更高效</p>
        </div>

        <div class="features">
            <div class="feature-card">
                <div class="feature-icon">📁</div>
                <h3>智能文件管理</h3>
                <p>支持多种文件格式的上传、预览和管理。拖拽上传，批量操作，让文件管理变得简单高效。</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">👥</div>
                <h3>实时协同编辑</h3>
                <p>多人同时编辑同一文档，实时同步更改，智能冲突解决，让团队协作无缝衔接。</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">🔍</div>
                <h3>全文搜索</h3>
                <p>强大的搜索功能，支持文件名、内容、标签等多维度搜索，快速找到所需文件。</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">🔒</div>
                <h3>权限管理</h3>
                <p>细粒度的权限控制，支持文件夹级别和文件级别的访问控制，保障数据安全。</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">📱</div>
                <h3>移动端适配</h3>
                <p>响应式设计，完美适配各种设备，随时随地访问您的工作空间。</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">⚡</div>
                <h3>高性能预览</h3>
                <p>支持PDF、Office文档、图片、代码等多种格式的在线预览，无需下载即可查看。</p>
            </div>
        </div>

        <div class="demo-section">
            <h2>📋 支持的文件格式</h2>
            <div class="file-list">
                <div class="file-item">
                    <div class="file-icon">📄</div>
                    <div class="file-name">PDF 文档</div>
                    <div class="file-size">支持在线预览</div>
                </div>
                <div class="file-item">
                    <div class="file-icon">📝</div>
                    <div class="file-name">Word 文档</div>
                    <div class="file-size">支持在线编辑</div>
                </div>
                <div class="file-item">
                    <div class="file-icon">📊</div>
                    <div class="file-name">Excel 表格</div>
                    <div class="file-size">支持数据预览</div>
                </div>
                <div class="file-item">
                    <div class="file-icon">🖼️</div>
                    <div class="file-name">图片文件</div>
                    <div class="file-size">JPG, PNG, SVG</div>
                </div>
                <div class="file-item">
                    <div class="file-icon">💻</div>
                    <div class="file-name">代码文件</div>
                    <div class="file-size">语法高亮显示</div>
                </div>
                <div class="file-item">
                    <div class="file-icon">🎵</div>
                    <div class="file-name">媒体文件</div>
                    <div class="file-size">音频、视频播放</div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>📈 系统统计</h2>
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number">1,234</div>
                    <div class="stat-label">活跃用户</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">5,678</div>
                    <div class="stat-label">文件总数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">89</div>
                    <div class="stat-label">工作空间</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">99.9%</div>
                    <div class="stat-label">系统可用性</div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>&copy; 2024 工作空间管理系统. 让协作更简单，让工作更高效。</p>
        </div>
    </div>

    <script>
        // 简单的交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为文件项添加点击效果
            const fileItems = document.querySelectorAll('.file-item');
            fileItems.forEach(item => {
                item.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });

            // 统计数字动画
            const statNumbers = document.querySelectorAll('.stat-number');
            statNumbers.forEach(stat => {
                const finalNumber = stat.textContent;
                if (!isNaN(parseFloat(finalNumber))) {
                    animateNumber(stat, 0, parseFloat(finalNumber.replace(/,/g, '')), 2000);
                }
            });
        });

        function animateNumber(element, start, end, duration) {
            const startTime = performance.now();
            const isPercentage = element.textContent.includes('%');
            
            function update(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                const current = start + (end - start) * easeOutCubic(progress);
                
                if (isPercentage) {
                    element.textContent = current.toFixed(1) + '%';
                } else if (end >= 1000) {
                    element.textContent = Math.floor(current).toLocaleString();
                } else {
                    element.textContent = Math.floor(current);
                }
                
                if (progress < 1) {
                    requestAnimationFrame(update);
                }
            }
            
            requestAnimationFrame(update);
        }

        function easeOutCubic(t) {
            return 1 - Math.pow(1 - t, 3);
        }
    </script>
</body>
</html>
