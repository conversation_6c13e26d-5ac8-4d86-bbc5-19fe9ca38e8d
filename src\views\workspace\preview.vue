<template>
  <div class="file-preview">
    <!-- 头部工具栏 -->
    <div class="preview-header">
      <div class="header-left">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item :to="{ path: '/workspace' }">工作空间</el-breadcrumb-item>
          <el-breadcrumb-item :to="{ path: `/workspace/${workspaceId}/files` }">{{ workspaceName }}</el-breadcrumb-item>
          <el-breadcrumb-item>{{ fileInfo.name }}</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="header-right">
        <el-button v-if="canEdit" :type="isEditing ? 'success' : 'primary'"
          :icon="isEditing ? 'el-icon-check' : 'el-icon-edit'" @click="toggleEdit" size="small">
          {{ isEditing ? '保存' : '编辑' }}
        </el-button>
        <el-button type="info" icon="el-icon-download" @click="handleDownload" size="small">
          下载
        </el-button>
        <el-button type="warning" icon="el-icon-share" @click="handleShare" size="small">
          分享
        </el-button>
        <el-button icon="el-icon-close" @click="handleClose" size="small">
          关闭
        </el-button>
      </div>
    </div>

    <!-- 预览内容区域 -->
    <div class="preview-content" v-loading="loading">
      <!-- 图片预览 -->
      <div v-if="fileType === 'image'" class="image-preview">
        <img :src="fileUrl" :alt="fileInfo.name" @load="handleImageLoad" />
      </div>

      <!-- PDF预览 -->
      <div v-else-if="fileType === 'pdf'" class="pdf-preview">
        <PdfViewer :src="fileUrl" />
      </div>

      <!-- 文档预览（Word, Excel, PowerPoint） -->
      <div v-else-if="fileType === 'office'" class="office-preview">
        <OfficeViewer :src="fileUrl" :fileName="fileInfo.name" :fileSize="fileInfo.size" />
      </div>

      <!-- 文本文件预览/编辑 -->
      <div v-else-if="fileType === 'text'" class="text-preview">
        <!-- 文本工具栏 -->
        <div class="text-toolbar">
          <el-button-group size="small">
            <el-button :type="!isEditing && !isCollaborating ? 'primary' : ''" @click="setTextMode('view')">
              <i class="el-icon-view"></i> 预览
            </el-button>
            <el-button :type="isEditing && !isCollaborating ? 'primary' : ''" @click="setTextMode('edit')">
              <i class="el-icon-edit"></i> 编辑
            </el-button>
            <el-button :type="isCollaborating ? 'primary' : ''" @click="setTextMode('collaborate')">
              <i class="el-icon-user"></i> 协作编辑
            </el-button>
          </el-button-group>

          <!-- 协作状态指示器 -->
          <div v-if="isCollaborating" class="collaboration-status">
            <el-tag size="mini" type="success">
              <i class="el-icon-connection"></i> 协作模式
            </el-tag>
            <span class="collaborator-count">{{ activeCollaborators.length }} 人在线</span>
          </div>
        </div>

        <!-- 预览模式 -->
        <div v-if="!isEditing && !isCollaborating" class="text-content" v-html="renderedTextContent"></div>

        <!-- 简单编辑模式 -->
        <textarea v-else-if="isEditing && !isCollaborating" v-model="editableContent" class="text-editor"
          @input="handleContentChange"></textarea>

        <!-- 协作编辑模式 -->
        <div v-else-if="isCollaborating" class="collaborative-text-editor">
          <CollaborativeEditor v-model="editableContent" :document-id="fileId"
            @content-change="handleCollaborativeChange" />
        </div>
      </div>

      <!-- Markdown预览/编辑 -->
      <div v-else-if="fileType === 'markdown'" class="markdown-preview">
        <div class="markdown-toolbar" v-if="isEditing">
          <el-button-group size="mini">
            <el-button @click="insertMarkdown('**', '**')" title="粗体">B</el-button>
            <el-button @click="insertMarkdown('*', '*')" title="斜体">I</el-button>
            <el-button @click="insertMarkdown('# ', '')" title="标题">H</el-button>
            <el-button @click="insertMarkdown('- ', '')" title="列表">列表</el-button>
            <el-button @click="insertMarkdown('```\n', '\n```')" title="代码">代码</el-button>
          </el-button-group>
        </div>
        <div class="markdown-container" :class="{ 'editing': isEditing }">
          <textarea v-if="isEditing" ref="markdownEditor" v-model="editableContent" class="markdown-editor"
            @input="handleContentChange" @scroll="handleEditorScroll"></textarea>
          <div v-if="!isEditing || viewMode === 'split'" class="markdown-rendered" ref="markdownPreview"
            v-html="renderedMarkdownContent"></div>
        </div>
      </div>

      <!-- 视频预览 -->
      <div v-else-if="fileType === 'video'" class="video-preview">
        <video :src="fileUrl" controls width="100%" height="auto">
          您的浏览器不支持视频播放
        </video>
      </div>

      <!-- 音频预览 -->
      <div v-else-if="fileType === 'audio'" class="audio-preview">
        <div class="audio-player">
          <audio :src="fileUrl" controls>
            您的浏览器不支持音频播放
          </audio>
          <div class="audio-info">
            <h3>{{ fileInfo.name }}</h3>
            <p>大小: {{ formatFileSize(fileInfo.size) }}</p>
          </div>
        </div>
      </div>

      <!-- 代码预览/编辑 -->
      <div v-else-if="fileType === 'code'" class="code-preview">
        <div class="code-toolbar" v-if="isEditing">
          <el-select v-model="codeLanguage" size="mini" style="width: 120px">
            <el-option label="JavaScript" value="javascript" />
            <el-option label="Python" value="python" />
            <el-option label="Java" value="java" />
            <el-option label="CSS" value="css" />
            <el-option label="HTML" value="html" />
            <el-option label="JSON" value="json" />
          </el-select>
        </div>
        <div v-if="!isEditing" class="code-content">
          <pre><code :class="`language-${codeLanguage}`" v-html="highlightedCode"></code></pre>
        </div>
        <textarea v-else v-model="editableContent" class="code-editor" @input="handleContentChange"></textarea>
      </div>

      <!-- 不支持的文件类型 -->
      <div v-else class="unsupported-preview">
        <div class="unsupported-content">
          <i class="el-icon-warning"></i>
          <h3>无法预览此文件类型</h3>
          <p>{{ fileInfo.name }}</p>
          <p>文件大小: {{ formatFileSize(fileInfo.size) }}</p>
          <el-button type="primary" @click="handleDownload">下载文件</el-button>
        </div>
      </div>
    </div>

    <!-- 协作用户列表 -->
    <div class="collaboration-panel" v-if="collaborators.length > 0">
      <div class="collaboration-header">
        <span>协作用户 ({{ collaborators.length }})</span>
      </div>
      <div class="collaboration-users">
        <div v-for="user in collaborators" :key="user.id" class="user-item" :class="{ 'active': user.isActive }">
          <el-avatar :src="user.avatar" :size="24">
            {{ user.nickname ? user.nickname.charAt(0) : 'U' }}
          </el-avatar>
          <span class="user-name">{{ user.nickname }}</span>
          <span v-if="user.isEditing" class="editing-indicator">编辑中</span>
        </div>
      </div>
    </div>

    <!-- 分享对话框 -->
    <el-dialog title="分享文件" :visible.sync="shareDialogVisible" width="500px">
      <div class="share-content">
        <div class="share-link">
          <el-input v-model="shareLink" readonly>
            <el-button slot="append" @click="copyShareLink">复制链接</el-button>
          </el-input>
        </div>
        <div class="share-options">
          <el-form label-width="80px">
            <el-form-item label="权限">
              <el-radio-group v-model="sharePermission">
                <el-radio label="read">只读</el-radio>
                <el-radio label="edit">可编辑</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="有效期">
              <el-select v-model="shareExpire">
                <el-option label="1天" value="1" />
                <el-option label="7天" value="7" />
                <el-option label="30天" value="30" />
                <el-option label="永久" value="0" />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </el-dialog>

    <!-- AI问答小组件 -->
    <AiChatWidget :context="{ type: 'document', fileId: fileId, fileName: fileInfo.name }" />
  </div>
</template>

<script>
import { marked } from 'marked'
import collaborationService from '@/utils/collaborationService'
import PdfViewer from '@/components/PdfViewer.vue'
import OfficeViewer from '@/components/OfficeViewer.vue'
import CollaborativeEditor from '@/components/CollaborativeEditor.vue'
import AiChatWidget from '@/components/AiChatWidget.vue'

export default {
  name: 'FilePreview',
  components: {
    PdfViewer,
    OfficeViewer,
    CollaborativeEditor,
    AiChatWidget
  },
  data () {
    return {
      loading: false,
      workspaceId: null,
      workspaceName: '',
      fileId: null,
      fileInfo: {},
      fileContent: '',
      editableContent: '',
      isEditing: false,
      viewMode: 'preview', // preview, split
      codeLanguage: 'javascript',

      // 协作相关
      collaborators: [],
      activeCollaborators: [],
      connectionId: null,
      isCollaborating: false,

      // 分享相关
      shareDialogVisible: false,
      shareLink: '',
      sharePermission: 'read',
      shareExpire: '7'
    }
  },
  computed: {
    fileType () {
      if (!this.fileInfo.name) return 'unknown'

      const ext = this.fileInfo.name.split('.').pop().toLowerCase()

      // 图片类型
      if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(ext)) {
        return 'image'
      }

      // PDF
      if (ext === 'pdf') {
        return 'pdf'
      }

      // Office文档
      if (['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'].includes(ext)) {
        return 'office'
      }

      // 文本文件
      if (['txt', 'log', 'ini', 'cfg', 'conf'].includes(ext)) {
        return 'text'
      }

      // Markdown
      if (['md', 'markdown'].includes(ext)) {
        return 'markdown'
      }

      // 代码文件
      if (['js', 'ts', 'vue', 'jsx', 'tsx', 'py', 'java', 'cpp', 'c', 'css', 'scss', 'less', 'html', 'xml', 'json', 'yaml', 'yml'].includes(ext)) {
        return 'code'
      }

      // 视频
      if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'].includes(ext)) {
        return 'video'
      }

      // 音频
      if (['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma'].includes(ext)) {
        return 'audio'
      }

      return 'unknown'
    },

    canEdit () {
      return ['text', 'markdown', 'code'].includes(this.fileType)
    },

    fileUrl () {
      // 为测试文件提供静态文件路径
      const staticFileMap = {
        '15': '/mock-files/sample-document.md',
        '16': '/mock-files/config.json',
        '17': '/mock-files/sample-code.js',
        '18': '/mock-files/readme.txt',
        '19': '/mock-files/logo.svg',
        '20': '/mock-files/demo.html',
        '21': '/mock-files/project-presentation.pptx',
        '22': '/mock-files/requirements-document.docx',
        '23': '/mock-files/project-data.xlsx',
        '24': '/mock-files/user-manual.pdf'
      }

      if (staticFileMap[this.fileId]) {
        return staticFileMap[this.fileId]
      }

      // 模拟文件URL
      return `${process.env.VUE_APP_BASE_API}/workspace/${this.workspaceId}/files/${this.fileId}/content`
    },



    renderedTextContent () {
      return this.fileContent.replace(/\n/g, '<br>')
    },

    renderedMarkdownContent () {
      return marked(this.isEditing ? this.editableContent : this.fileContent)
    },

    highlightedCode () {
      // 简单的代码高亮，实际项目中可以使用 highlight.js
      return this.fileContent.replace(/</g, '&lt;').replace(/>/g, '&gt;')
    }
  },
  created () {
    this.workspaceId = this.$route.params.id
    this.fileId = this.$route.params.fileId
    this.loadFileInfo()
    this.loadFileContent()
    this.initCollaboration()
  },
  beforeDestroy () {
    this.disconnectCollaboration()
  },
  methods: {
    // 加载文件信息
    async loadFileInfo () {
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 300))

        const mockFiles = {
          1: { name: '项目文档.pdf', size: 2048576, type: 'file' },
          2: { name: '需求分析报告.docx', size: 1024000, type: 'file' },
          3: { name: '系统架构图.png', size: 512000, type: 'file' },
          4: { name: '数据库设计.xlsx', size: 256000, type: 'file' },
          5: { name: '用户手册.pdf', size: 3072000, type: 'file' },
          6: { name: '项目笔记.md', size: 15360, type: 'note' },
          7: { name: '会议纪要.md', size: 8192, type: 'note' },
          8: { name: 'config.json', size: 1024, type: 'file' },
          9: { name: 'demo.mp4', size: 10485760, type: 'file' },
          10: { name: 'background.mp3', size: 5242880, type: 'file' },
          // 新增的真实测试文件
          15: { name: 'sample-document.md', size: 12800, type: 'file' },
          16: { name: 'config.json', size: 4096, type: 'file' },
          17: { name: 'sample-code.js', size: 8192, type: 'file' },
          18: { name: 'readme.txt', size: 6144, type: 'file' },
          19: { name: 'logo.svg', size: 3072, type: 'file' },
          20: { name: 'demo.html', size: 9216, type: 'file' },
          21: { name: 'project-presentation.pptx', size: 15360, type: 'file' },
          22: { name: 'requirements-document.docx', size: 18432, type: 'file' },
          23: { name: 'project-data.xlsx', size: 10240, type: 'file' },
          24: { name: 'user-manual.pdf', size: 12288, type: 'file' }
        }

        this.fileInfo = mockFiles[this.fileId] || { name: '未知文件', size: 0, type: 'file' }
        this.workspaceName = '研发团队协作空间'
      } catch (error) {
        this.$message.error('加载文件信息失败')
      }
    },

    // 加载文件内容
    async loadFileContent () {
      this.loading = true
      try {
        // 对于真实测试文件，从静态文件加载内容
        const staticFileMap = {
          '15': '/mock-files/sample-document.md',
          '16': '/mock-files/config.json',
          '17': '/mock-files/sample-code.js',
          '18': '/mock-files/readme.txt',
          '19': '/mock-files/logo.svg',
          '20': '/mock-files/demo.html',
          '21': '/mock-files/project-presentation.pptx',
          '22': '/mock-files/requirements-document.docx',
          '23': '/mock-files/project-data.xlsx',
          '24': '/mock-files/user-manual.pdf'
        }

        if (staticFileMap[this.fileId]) {
          const response = await fetch(staticFileMap[this.fileId])
          if (response.ok) {
            this.fileContent = await response.text()
            this.editableContent = this.fileContent
            return
          }
        }

        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 500))

        // 根据文件类型提供不同的模拟内容
        if (this.fileType === 'markdown') {
          this.fileContent = `# ${this.fileInfo.name}

这是一个Markdown文件的示例内容。

## 功能特性

- **实时预览**：支持Markdown实时渲染
- **协同编辑**：多人同时编辑
- **版本控制**：自动保存历史版本

### 代码示例

\`\`\`javascript
function hello() {
  console.log('Hello, World!')
}
\`\`\`

> 这是一个引用块

**粗体文本** 和 *斜体文本*

[链接示例](https://example.com)`
        } else if (this.fileType === 'text') {
          this.fileContent = `这是一个文本文件的示例内容。

文件名: ${this.fileInfo.name}
创建时间: ${new Date().toLocaleString()}

这里可以包含任何文本内容，支持在线编辑和协同编辑功能。`
        } else if (this.fileType === 'code') {
          this.fileContent = `{
  "name": "workspace-demo",
  "version": "1.0.0",
  "description": "工作空间演示项目",
  "main": "index.js",
  "scripts": {
    "start": "node index.js",
    "dev": "nodemon index.js",
    "test": "jest"
  },
  "dependencies": {
    "express": "^4.18.0",
    "socket.io": "^4.5.0"
  }
}`
          this.codeLanguage = 'json'
        }

        this.editableContent = this.fileContent
      } catch (error) {
        this.$message.error('加载文件内容失败')
      } finally {
        this.loading = false
      }
    },

    // 初始化协作
    initCollaboration () {
      if (!this.canEdit) return

      // 模拟当前用户信息
      const currentUser = {
        id: 1,
        nickname: '当前用户',
        avatar: ''
      }

      // 连接到协作服务
      this.connectionId = collaborationService.connect(
        `${this.workspaceId}_${this.fileId}`,
        currentUser
      )

      // 监听协作事件
      collaborationService.on(this.connectionId, 'operation', this.handleRemoteOperation)
      collaborationService.on(this.connectionId, 'userJoined', this.handleUserJoined)
      collaborationService.on(this.connectionId, 'userLeft', this.handleUserLeft)

      this.isCollaborating = true
      this.updateCollaborators()
    },

    // 断开协作连接
    disconnectCollaboration () {
      if (this.connectionId) {
        collaborationService.disconnect(this.connectionId)
        this.connectionId = null
        this.isCollaborating = false
      }
    },

    // 更新协作用户列表
    updateCollaborators () {
      if (this.connectionId) {
        this.collaborators = collaborationService.getCollaborators(`${this.workspaceId}_${this.fileId}`)
      } else {
        // 模拟协作用户（非协作模式）
        this.collaborators = [
          { id: 1, nickname: '张三', avatar: '', isActive: true, isEditing: false },
          { id: 2, nickname: '李四', avatar: '', isActive: true, isEditing: true },
          { id: 3, nickname: '王五', avatar: '', isActive: false, isEditing: false }
        ]
      }
    },

    // 处理远程操作
    handleRemoteOperation (operation) {
      // 应用远程操作到本地内容
      switch (operation.type) {
        case 'replace':
          this.editableContent = operation.content
          this.fileContent = operation.content
          break
        case 'insert':
          this.editableContent =
            this.editableContent.slice(0, operation.position) +
            operation.content +
            this.editableContent.slice(operation.position)
          break
        case 'delete':
          this.editableContent =
            this.editableContent.slice(0, operation.position) +
            this.editableContent.slice(operation.position + operation.length)
          break
      }
    },

    // 处理用户加入
    handleUserJoined (userInfo) {
      this.updateCollaborators()
      this.$message.info(`${userInfo.nickname} 加入了协作`)
    },

    // 处理用户离开
    handleUserLeft (userInfo) {
      this.updateCollaborators()
      this.$message.info(`${userInfo.nickname} 离开了协作`)
    },

    // 切换编辑模式
    toggleEdit () {
      if (this.isEditing) {
        this.saveContent()
      } else {
        this.isEditing = true
      }
    },

    // 设置文本模式
    setTextMode (mode) {
      switch (mode) {
        case 'view':
          this.isEditing = false
          this.isCollaborating = false
          break
        case 'edit':
          this.isEditing = true
          this.isCollaborating = false
          this.editableContent = this.fileContent
          break
        case 'collaborate':
          this.isEditing = false
          this.isCollaborating = true
          this.editableContent = this.fileContent
          this.$message.success('协作模式已启用')
          break
      }
    },

    // 处理协作内容变化
    handleCollaborativeChange (content) {
      this.editableContent = content
      this.hasUnsavedChanges = true

      // 同步内容变化到其他协作者
      if (this.connectionId) {
        collaborationService.sendOperation(this.connectionId, {
          type: 'content_change',
          content: content,
          timestamp: Date.now()
        })
      }
    },

    // 保存内容
    async saveContent () {
      try {
        // 模拟保存API调用
        await new Promise(resolve => setTimeout(resolve, 500))
        this.fileContent = this.editableContent
        this.isEditing = false
        this.$message.success('保存成功')
      } catch (error) {
        this.$message.error('保存失败')
      }
    },

    // 处理内容变化
    handleContentChange () {
      // 实时协作功能
      if (this.isCollaborating && this.connectionId) {
        // 更新用户活动时间
        collaborationService.updateActivity(this.connectionId)

        // 发送内容变更操作
        const operation = {
          type: 'replace',
          content: this.editableContent,
          position: 0,
          timestamp: Date.now()
        }

        collaborationService.sendOperation(this.connectionId, operation)
        this.updateCollaborators()
      }
    },

    // 插入Markdown语法
    insertMarkdown (before, after) {
      const editor = this.$refs.markdownEditor
      if (!editor) return

      const start = editor.selectionStart
      const end = editor.selectionEnd
      const selectedText = this.editableContent.substring(start, end)

      const newText = before + selectedText + after
      this.editableContent = this.editableContent.substring(0, start) + newText + this.editableContent.substring(end)

      this.$nextTick(() => {
        editor.focus()
        editor.setSelectionRange(start + before.length, start + before.length + selectedText.length)
      })
    },

    // 处理编辑器滚动同步
    handleEditorScroll () {
      if (this.viewMode === 'split' && this.$refs.markdownPreview) {
        const editor = this.$refs.markdownEditor
        const preview = this.$refs.markdownPreview
        const scrollRatio = editor.scrollTop / (editor.scrollHeight - editor.clientHeight)
        preview.scrollTop = scrollRatio * (preview.scrollHeight - preview.clientHeight)
      }
    },

    // 处理图片加载
    handleImageLoad () {
      // 图片加载完成后的处理
    },

    // 下载文件
    handleDownload () {
      // 模拟下载
      const link = document.createElement('a')
      link.href = this.fileUrl
      link.download = this.fileInfo.name
      link.click()
      this.$message.success('开始下载')
    },

    // 分享文件
    handleShare () {
      this.shareLink = `${window.location.origin}/share/file/${this.fileId}?token=abc123`
      this.shareDialogVisible = true
    },

    // 复制分享链接
    copyShareLink () {
      navigator.clipboard.writeText(this.shareLink).then(() => {
        this.$message.success('链接已复制到剪贴板')
      }).catch(() => {
        this.$message.error('复制失败')
      })
    },

    // 关闭预览
    handleClose () {
      this.$router.go(-1)
    },

    // 格式化文件大小
    formatFileSize (size) {
      if (!size) return '-'
      const units = ['B', 'KB', 'MB', 'GB']
      let index = 0
      while (size >= 1024 && index < units.length - 1) {
        size /= 1024
        index++
      }
      return size.toFixed(2) + ' ' + units[index]
    }
  }
}
</script>

<style lang="scss" scoped>
.file-preview {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .header-left {
    ::v-deep .el-breadcrumb__inner {
      color: #606266;
      font-weight: normal;
    }

    ::v-deep .el-breadcrumb__inner.is-link {
      color: #409eff;

      &:hover {
        color: #66b1ff;
      }
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

.preview-content {
  flex: 1;
  display: flex;
  overflow: hidden;
  background: white;
}

// 图片预览
.image-preview {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;

  img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

// PDF预览
.pdf-preview,
.office-preview {
  width: 100%;

  iframe {
    border: none;
  }
}

// 文本预览
.text-preview {
  width: 100%;
  padding: 0;

  .text-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #fafbfc;
    border-bottom: 1px solid #e4e7ed;

    .collaboration-status {
      display: flex;
      align-items: center;
      gap: 12px;

      .collaborator-count {
        font-size: 12px;
        color: #606266;
      }
    }
  }

  .text-content {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    line-height: 1.6;
    white-space: pre-wrap;
    padding: 20px;
    min-height: 400px;
  }

  .text-editor {
    width: 100%;
    height: 500px;
    border: none;
    outline: none;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    line-height: 1.6;
    resize: none;
    padding: 20px;
  }

  .collaborative-text-editor {
    min-height: 500px;
    background: white;
  }
}

// Markdown预览
.markdown-preview {
  width: 100%;
  display: flex;
  flex-direction: column;

  .markdown-toolbar {
    padding: 12px 16px;
    border-bottom: 1px solid #e4e7ed;
    background: #fafbfc;
  }

  .markdown-container {
    flex: 1;
    display: flex;
    overflow: hidden;

    &.editing {
      .markdown-editor {
        width: 50%;
        border-right: 1px solid #e4e7ed;
      }

      .markdown-rendered {
        width: 50%;
      }
    }

    .markdown-editor {
      width: 100%;
      border: none;
      outline: none;
      padding: 20px;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 14px;
      line-height: 1.6;
      resize: none;
    }

    .markdown-rendered {
      width: 100%;
      padding: 20px;
      overflow-y: auto;

      ::v-deep {

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
          margin-top: 24px;
          margin-bottom: 16px;
          font-weight: 600;
          line-height: 1.25;
        }

        h1 {
          font-size: 2em;
          border-bottom: 1px solid #eaecef;
          padding-bottom: 8px;
        }

        h2 {
          font-size: 1.5em;
          border-bottom: 1px solid #eaecef;
          padding-bottom: 8px;
        }

        h3 {
          font-size: 1.25em;
        }

        p {
          margin-bottom: 16px;
          line-height: 1.6;
        }

        code {
          background: #f6f8fa;
          padding: 2px 4px;
          border-radius: 3px;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 85%;
        }

        pre {
          background: #f6f8fa;
          padding: 16px;
          border-radius: 6px;
          overflow-x: auto;
          margin-bottom: 16px;

          code {
            background: none;
            padding: 0;
          }
        }

        blockquote {
          border-left: 4px solid #dfe2e5;
          padding-left: 16px;
          margin-left: 0;
          margin-bottom: 16px;
          color: #6a737d;
        }

        ul,
        ol {
          margin-bottom: 16px;
          padding-left: 24px;
        }

        li {
          margin-bottom: 4px;
        }
      }
    }
  }
}

// 视频预览
.video-preview {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

// 音频预览
.audio-preview {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;

  .audio-player {
    text-align: center;

    audio {
      margin-bottom: 20px;
    }

    .audio-info {
      h3 {
        margin: 0 0 8px 0;
        color: #303133;
      }

      p {
        margin: 0;
        color: #909399;
        font-size: 14px;
      }
    }
  }
}

// 代码预览
.code-preview {
  width: 100%;
  display: flex;
  flex-direction: column;

  .code-toolbar {
    padding: 12px 16px;
    border-bottom: 1px solid #e4e7ed;
    background: #fafbfc;
  }

  .code-content {
    flex: 1;
    overflow: auto;
    padding: 20px;

    pre {
      margin: 0;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 14px;
      line-height: 1.6;
    }
  }

  .code-editor {
    flex: 1;
    border: none;
    outline: none;
    padding: 20px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    line-height: 1.6;
    resize: none;
  }
}

// 不支持的文件类型
.unsupported-preview {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  .unsupported-content {
    text-align: center;
    color: #909399;

    i {
      font-size: 64px;
      color: #dcdfe6;
      margin-bottom: 16px;
    }

    h3 {
      margin: 0 0 8px 0;
      color: #606266;
    }

    p {
      margin: 0 0 8px 0;
      font-size: 14px;
    }

    .el-button {
      margin-top: 16px;
    }
  }
}

// 协作面板
.collaboration-panel {
  position: fixed;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  width: 200px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;

  .collaboration-header {
    padding: 12px 16px;
    border-bottom: 1px solid #e4e7ed;
    font-weight: 600;
    font-size: 14px;
    color: #303133;
  }

  .collaboration-users {
    padding: 8px;

    .user-item {
      display: flex;
      align-items: center;
      padding: 8px;
      border-radius: 4px;
      margin-bottom: 4px;
      transition: all 0.3s ease;

      &.active {
        background: #f0f9ff;
      }

      .user-name {
        margin-left: 8px;
        font-size: 12px;
        color: #606266;
        flex: 1;
      }

      .editing-indicator {
        font-size: 10px;
        color: #67c23a;
        background: #f0f9ff;
        padding: 2px 6px;
        border-radius: 10px;
      }
    }
  }
}

// 分享对话框
.share-content {
  .share-link {
    margin-bottom: 20px;
  }

  .share-options {
    .el-form-item {
      margin-bottom: 16px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .preview-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .collaboration-panel {
    position: static;
    transform: none;
    width: 100%;
    margin-top: 20px;
  }

  .markdown-container.editing {
    flex-direction: column;

    .markdown-editor,
    .markdown-rendered {
      width: 100%;
      height: 50%;
    }
  }
}
</style>
