/**
 * 工作空间文件管理器
 * 提供文件上传、下载、预览等核心功能
 */

import axios from 'axios'
import { EventEmitter } from 'events'

class WorkspaceFileManager extends EventEmitter {
  constructor(options = {}) {
    super()
    this.baseURL = options.baseURL || '/api'
    this.maxFileSize = options.maxFileSize || 50 * 1024 * 1024 // 50MB
    this.allowedTypes = options.allowedTypes || [
      'image/*',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.*',
      'text/*'
    ]
    this.uploadQueue = []
    this.isUploading = false
  }

  /**
   * 上传文件
   * @param {File|FileList} files - 要上传的文件
   * @param {Object} options - 上传选项
   * @returns {Promise} 上传结果
   */
  async uploadFiles(files, options = {}) {
    const fileArray = Array.from(files)
    const validFiles = this.validateFiles(fileArray)
    
    if (validFiles.length === 0) {
      throw new Error('没有有效的文件可以上传')
    }

    const uploadPromises = validFiles.map(file => this.uploadSingleFile(file, options))
    
    try {
      const results = await Promise.allSettled(uploadPromises)
      const successful = results.filter(r => r.status === 'fulfilled').map(r => r.value)
      const failed = results.filter(r => r.status === 'rejected').map(r => r.reason)
      
      this.emit('uploadComplete', { successful, failed })
      return { successful, failed }
    } catch (error) {
      this.emit('uploadError', error)
      throw error
    }
  }

  /**
   * 上传单个文件
   * @param {File} file - 文件对象
   * @param {Object} options - 上传选项
   * @returns {Promise} 上传结果
   */
  async uploadSingleFile(file, options = {}) {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('workspaceId', options.workspaceId || '')
    formData.append('folderId', options.folderId || '')

    const config = {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: (progressEvent) => {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        this.emit('uploadProgress', { file, progress })
      }
    }

    try {
      const response = await axios.post(`${this.baseURL}/files/upload`, formData, config)
      this.emit('fileUploaded', { file, response: response.data })
      return response.data
    } catch (error) {
      this.emit('uploadError', { file, error })
      throw error
    }
  }

  /**
   * 验证文件
   * @param {File[]} files - 文件数组
   * @returns {File[]} 有效的文件数组
   */
  validateFiles(files) {
    return files.filter(file => {
      // 检查文件大小
      if (file.size > this.maxFileSize) {
        this.emit('validationError', {
          file,
          error: `文件大小超过限制 (${this.formatFileSize(this.maxFileSize)})`
        })
        return false
      }

      // 检查文件类型
      const isValidType = this.allowedTypes.some(type => {
        if (type.endsWith('*')) {
          return file.type.startsWith(type.slice(0, -1))
        }
        return file.type === type
      })

      if (!isValidType) {
        this.emit('validationError', {
          file,
          error: '不支持的文件类型'
        })
        return false
      }

      return true
    })
  }

  /**
   * 下载文件
   * @param {string} fileId - 文件ID
   * @param {string} fileName - 文件名
   * @returns {Promise} 下载结果
   */
  async downloadFile(fileId, fileName) {
    try {
      const response = await axios.get(`${this.baseURL}/files/${fileId}/download`, {
        responseType: 'blob'
      })

      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', fileName)
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      this.emit('fileDownloaded', { fileId, fileName })
      return true
    } catch (error) {
      this.emit('downloadError', { fileId, fileName, error })
      throw error
    }
  }

  /**
   * 获取文件列表
   * @param {Object} params - 查询参数
   * @returns {Promise} 文件列表
   */
  async getFileList(params = {}) {
    try {
      const response = await axios.get(`${this.baseURL}/files`, { params })
      return response.data
    } catch (error) {
      this.emit('error', error)
      throw error
    }
  }

  /**
   * 删除文件
   * @param {string|string[]} fileIds - 文件ID或ID数组
   * @returns {Promise} 删除结果
   */
  async deleteFiles(fileIds) {
    const ids = Array.isArray(fileIds) ? fileIds : [fileIds]
    
    try {
      const response = await axios.delete(`${this.baseURL}/files`, {
        data: { fileIds: ids }
      })
      
      this.emit('filesDeleted', { fileIds: ids })
      return response.data
    } catch (error) {
      this.emit('deleteError', { fileIds: ids, error })
      throw error
    }
  }

  /**
   * 重命名文件
   * @param {string} fileId - 文件ID
   * @param {string} newName - 新文件名
   * @returns {Promise} 重命名结果
   */
  async renameFile(fileId, newName) {
    try {
      const response = await axios.put(`${this.baseURL}/files/${fileId}/rename`, {
        name: newName
      })
      
      this.emit('fileRenamed', { fileId, newName })
      return response.data
    } catch (error) {
      this.emit('renameError', { fileId, newName, error })
      throw error
    }
  }

  /**
   * 获取文件预览URL
   * @param {string} fileId - 文件ID
   * @returns {string} 预览URL
   */
  getPreviewUrl(fileId) {
    return `${this.baseURL}/files/${fileId}/preview`
  }

  /**
   * 格式化文件大小
   * @param {number} bytes - 字节数
   * @returns {string} 格式化后的大小
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B'
    
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  /**
   * 获取文件图标
   * @param {string} fileName - 文件名
   * @returns {string} 图标类名
   */
  getFileIcon(fileName) {
    const ext = fileName.split('.').pop().toLowerCase()
    const iconMap = {
      // 文档类
      pdf: 'el-icon-document',
      doc: 'el-icon-document',
      docx: 'el-icon-document',
      
      // 表格类
      xls: 'el-icon-s-grid',
      xlsx: 'el-icon-s-grid',
      csv: 'el-icon-s-grid',
      
      // 演示文稿
      ppt: 'el-icon-present',
      pptx: 'el-icon-present',
      
      // 图片类
      jpg: 'el-icon-picture',
      jpeg: 'el-icon-picture',
      png: 'el-icon-picture',
      gif: 'el-icon-picture',
      svg: 'el-icon-picture',
      
      // 视频类
      mp4: 'el-icon-video-camera',
      avi: 'el-icon-video-camera',
      mov: 'el-icon-video-camera',
      
      // 音频类
      mp3: 'el-icon-headset',
      wav: 'el-icon-headset',
      
      // 代码类
      js: 'el-icon-document',
      ts: 'el-icon-document',
      vue: 'el-icon-document',
      html: 'el-icon-document',
      css: 'el-icon-document',
      
      // 压缩包
      zip: 'el-icon-folder-opened',
      rar: 'el-icon-folder-opened',
      '7z': 'el-icon-folder-opened'
    }
    
    return iconMap[ext] || 'el-icon-document'
  }
}

// 导出文件管理器类
export default WorkspaceFileManager

// 创建默认实例
export const fileManager = new WorkspaceFileManager()

// 工具函数
export const utils = {
  /**
   * 检查文件是否为图片
   * @param {string} fileName - 文件名
   * @returns {boolean} 是否为图片
   */
  isImage(fileName) {
    const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg']
    const ext = fileName.split('.').pop().toLowerCase()
    return imageExts.includes(ext)
  },

  /**
   * 检查文件是否可预览
   * @param {string} fileName - 文件名
   * @returns {boolean} 是否可预览
   */
  isPreviewable(fileName) {
    const previewableExts = [
      'jpg', 'jpeg', 'png', 'gif', 'svg', 'webp',
      'pdf', 'txt', 'md', 'json', 'xml', 'yaml',
      'js', 'ts', 'vue', 'html', 'css', 'py', 'java'
    ]
    const ext = fileName.split('.').pop().toLowerCase()
    return previewableExts.includes(ext)
  },

  /**
   * 生成唯一ID
   * @returns {string} 唯一ID
   */
  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }
}
