<template>
  <div class="pdf-viewer">
    <div class="pdf-toolbar">
      <div class="toolbar-left">
        <el-button-group size="small">
          <el-button icon="el-icon-minus" @click="zoomOut" :disabled="scale <= 0.5">缩小</el-button>
          <el-button @click="resetZoom">{{ Math.round(scale * 100) }}%</el-button>
          <el-button icon="el-icon-plus" @click="zoomIn" :disabled="scale >= 3">放大</el-button>
        </el-button-group>
      </div>
      <div class="toolbar-center">
        <el-button-group size="small">
          <el-button icon="el-icon-arrow-left" @click="prevPage" :disabled="currentPage <= 1">上一页</el-button>
          <el-input v-model="pageInput" size="small" style="width: 60px; text-align: center" @keyup.enter="goToPage"
            @blur="goToPage" />
          <span class="page-info">/ {{ totalPages }}</span>
          <el-button icon="el-icon-arrow-right" @click="nextPage" :disabled="currentPage >= totalPages">下一页</el-button>
        </el-button-group>
      </div>
      <div class="toolbar-right">
        <el-button-group size="small">
          <el-button icon="el-icon-refresh-left" @click="rotateLeft">逆时针</el-button>
          <el-button icon="el-icon-refresh-right" @click="rotateRight">顺时针</el-button>
        </el-button-group>
      </div>
    </div>

    <div class="pdf-content" ref="pdfContainer" v-loading="loading">
      <div class="pdf-demo-container" :style="{ transform: `scale(${scale}) rotate(${rotation}deg)` }">
        <!-- 真实PDF内容 -->
        <div v-if="pdfData && pdfData.pages" class="pdf-page-demo">
          <div v-html="pdfData.pages[currentPage - 1]?.content || '<div class=\" page-content\">页面内容加载中...</div>'">
        </div>
      </div>

      <!-- 默认模拟内容 -->
      <template v-else>
        <div class="pdf-page-demo" v-show="currentPage === 1">
          <div class="page-header">第 1 页</div>
          <div class="page-content">
            <h2>项目文档</h2>
            <p>这是一个PDF文档预览示例</p>
            <h3>项目概述</h3>
            <ul>
              <li>工作空间管理系统</li>
              <li>文件管理功能</li>
              <li>协同编辑功能</li>
              <li>实时预览功能</li>
            </ul>
          </div>
        </div>

        <div class="pdf-page-demo" v-show="currentPage === 2">
          <div class="page-header">第 2 页</div>
          <div class="page-content">
            <h3>技术架构</h3>
            <p>前端：Vue.js + Element UI</p>
            <p>后端：Node.js + Express</p>
            <p>数据库：MongoDB</p>
            <h3>核心功能</h3>
            <ul>
              <li>文件上传下载</li>
              <li>多格式预览</li>
              <li>实时协作</li>
            </ul>
          </div>
        </div>

        <div class="pdf-page-demo" v-show="currentPage === 3">
          <div class="page-header">第 3 页</div>
          <div class="page-content">
            <h3>开发规范</h3>
            <p>代码规范、测试要求、部署流程</p>
            <h3>项目进度</h3>
            <p>当前进度：75%</p>
            <p>预计完成时间：2024年3月</p>
          </div>
        </div>
      </template>
    </div>

    <div v-if="error" class="pdf-error">
      <i class="el-icon-warning"></i>
      <h3>PDF加载失败</h3>
      <p>{{ error }}</p>
      <el-button @click="reload">重新加载</el-button>
    </div>
  </div>
  </div>
</template>

<script>
export default {
  name: 'PdfViewer',
  props: {
    src: {
      type: String,
      required: true
    }
  },
  data () {
    return {
      loading: false,
      error: null,
      currentPage: 1,
      totalPages: 3,
      pageInput: '1',
      scale: 1.0,
      rotation: 0,
      pdfData: null
    }
  },
  watch: {
    currentPage (newPage) {
      this.pageInput = newPage.toString()
    }
  },
  mounted () {
    this.loadPdf()
  },
  methods: {
    prevPage () {
      if (this.currentPage > 1) {
        this.currentPage--
      }
    },

    nextPage () {
      if (this.currentPage < this.totalPages) {
        this.currentPage++
      }
    },

    goToPage () {
      const page = parseInt(this.pageInput)
      if (page >= 1 && page <= this.totalPages) {
        this.currentPage = page
      } else {
        this.pageInput = this.currentPage.toString()
      }
    },

    zoomIn () {
      this.scale = Math.min(this.scale + 0.25, 3)
    },

    zoomOut () {
      this.scale = Math.max(this.scale - 0.25, 0.5)
    },

    resetZoom () {
      this.scale = 1.0
    },

    rotateLeft () {
      this.rotation = (this.rotation - 90) % 360
    },

    rotateRight () {
      this.rotation = (this.rotation + 90) % 360
    },

    async loadPdf () {
      this.loading = true
      this.error = null

      try {
        // 尝试从静态JSON文件加载真实PDF数据
        const jsonUrl = this.src + '.json'
        try {
          const response = await fetch(jsonUrl)
          if (response.ok) {
            const jsonData = await response.json()
            if (jsonData.type === 'pdf' && jsonData.pages) {
              this.totalPages = jsonData.pages.length
              this.pdfData = jsonData
              return
            }
          }
        } catch (jsonError) {
          console.log('PDF JSON文件加载失败，使用默认数据:', jsonError)
        }

        // 使用默认的模拟数据
        this.totalPages = 3
        this.pdfData = null
      } catch (error) {
        console.error('PDF加载失败:', error)
        this.error = error.message || 'PDF加载失败'
      } finally {
        this.loading = false
      }
    },

    reload () {
      this.loadPdf()
    }
  }
}
</script>

<style lang="scss" scoped>
.pdf-viewer {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.pdf-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .toolbar-center {
    display: flex;
    align-items: center;
    gap: 8px;

    .page-info {
      font-size: 14px;
      color: #606266;
      margin: 0 4px;
    }
  }
}

.pdf-content {
  flex: 1;
  overflow: auto;
  padding: 20px;
  display: flex;
  justify-content: center;
}

.pdf-demo-container {
  transition: transform 0.3s ease;
  transform-origin: center top;
}

.pdf-page-demo {
  display: block;
  margin: 0 auto 20px auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: white;
  padding: 40px;
  max-width: 600px;
  min-height: 400px;

  .page-header {
    text-align: center;
    font-size: 14px;
    color: #909399;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e4e7ed;
  }

  .page-content {

    h2,
    h3 {
      color: #303133;
      margin-bottom: 16px;
    }

    h2 {
      font-size: 24px;
      text-align: center;
    }

    h3 {
      font-size: 18px;
    }

    p {
      color: #606266;
      line-height: 1.6;
      margin-bottom: 12px;
    }

    ul {
      color: #606266;
      padding-left: 20px;

      li {
        margin-bottom: 8px;
      }
    }
  }
}

.pdf-error {
  text-align: center;
  color: #909399;
  padding: 60px 20px;

  i {
    font-size: 64px;
    color: #dcdfe6;
    margin-bottom: 16px;
  }

  h3 {
    margin: 0 0 8px 0;
    color: #606266;
  }

  p {
    margin: 0 0 16px 0;
    font-size: 14px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .pdf-toolbar {
    flex-direction: column;
    gap: 8px;
    padding: 12px;

    .toolbar-left,
    .toolbar-center,
    .toolbar-right {
      width: 100%;
      justify-content: center;
    }
  }

  .pdf-content {
    padding: 10px;
  }
}
</style>
